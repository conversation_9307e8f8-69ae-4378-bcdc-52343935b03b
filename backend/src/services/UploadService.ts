import fs from 'fs/promises';
import path from 'path';
import { mongoose } from '@/config/mongodb';
import { RAGService } from '@/services/RAGService';
import { AIService } from '@/services/AIService';
import { config } from '@/config/environment';
import { logger, logDatabaseOperation, logError } from '@/utils/logger';
import { DatabaseError, NotFoundError, ValidationError } from '@/middleware/errorHandler';
import { v4 as uuidv4 } from 'uuid';
import pdfParse from 'pdf-parse';
import mammoth from 'mammoth';
import axios from 'axios';

interface ProcessOptions {
  extractText?: boolean;
  addToKnowledgeBase?: boolean;
  source?: string;
  metadata?: any;
}

interface ListOptions {
  page?: number;
  limit?: number;
  type?: string;
  source?: string;
  search?: string;
}

interface BatchOptions {
  [key: string]: any;
}

// MongoDB schema for uploaded files
const fileSchema = new mongoose.Schema({
  id: { type: String, unique: true, required: true },
  originalName: { type: String, required: true },
  fileName: { type: String, required: true },
  filePath: { type: String, required: true },
  mimeType: { type: String, required: true },
  size: { type: Number, required: true },
  extractedText: { type: String },
  metadata: { type: mongoose.Schema.Types.Mixed },
  source: { type: String },
  processed: { type: Boolean, default: false },
  addedToKnowledgeBase: { type: Boolean, default: false },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

const FileModel = mongoose.model('UploadedFile', fileSchema);

export class UploadService {
  private ragService: RAGService;
  private aiService: AIService;

  constructor() {
    this.ragService = new RAGService();
    this.aiService = new AIService();
  }

  async processFile(file: Express.Multer.File, options: ProcessOptions): Promise<any> {
    const startTime = Date.now();

    try {
      const fileId = uuidv4();
      let extractedText = '';

      // Extract text if requested
      if (options.extractText) {
        extractedText = await this.extractTextFromFile(file);
      }

      // Save file metadata to database
      const fileDoc = new FileModel({
        id: fileId,
        originalName: file.originalname,
        fileName: file.filename,
        filePath: file.path,
        mimeType: file.mimetype,
        size: file.size,
        extractedText,
        metadata: options.metadata || {},
        source: options.source,
        processed: true,
        addedToKnowledgeBase: false,
      });

      await fileDoc.save();

      // Add to knowledge base if requested
      if (options.addToKnowledgeBase && extractedText) {
        await this.addToKnowledgeBase(fileId, extractedText, {
          title: file.originalname,
          source: options.source || 'file_upload',
          fileName: file.filename,
          mimeType: file.mimetype,
          ...options.metadata,
        });

        fileDoc.addedToKnowledgeBase = true;
        await fileDoc.save();
      }

      const duration = Date.now() - startTime;
      logDatabaseOperation('Process file', 'Upload', duration, {
        fileName: file.originalname,
        size: file.size,
        extractedText: !!extractedText,
        addedToKnowledgeBase: options.addToKnowledgeBase && !!extractedText,
      });

      return {
        id: fileId,
        originalName: file.originalname,
        fileName: file.filename,
        mimeType: file.mimetype,
        size: file.size,
        extractedText: extractedText ? extractedText.substring(0, 500) + '...' : null,
        addedToKnowledgeBase: fileDoc.addedToKnowledgeBase,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logError('Failed to process file', error, { fileName: file.originalname, options });
      throw new DatabaseError('File processing failed');
    }
  }

  async processFiles(files: Express.Multer.File[], options: ProcessOptions): Promise<any> {
    const startTime = Date.now();

    try {
      const results = [];
      const errors = [];

      for (const file of files) {
        try {
          const result = await this.processFile(file, options);
          results.push(result);
        } catch (error) {
          errors.push({
            fileName: file.originalname,
            error: error.message,
          });
        }
      }

      const duration = Date.now() - startTime;
      logDatabaseOperation('Process files', 'Upload', duration, {
        totalFiles: files.length,
        successful: results.length,
        errors: errors.length,
      });

      return {
        successful: results,
        errors,
        totalFiles: files.length,
        successCount: results.length,
        errorCount: errors.length,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logError('Failed to process files', error, { fileCount: files.length, options });
      throw new DatabaseError('Batch file processing failed');
    }
  }

  async processUrl(url: string, options: ProcessOptions): Promise<any> {
    const startTime = Date.now();

    try {
      // Download content from URL
      const response = await axios.get(url, {
        timeout: 30000,
        maxContentLength: config.upload.maxFileSize,
      });

      const content = response.data;
      const contentType = response.headers['content-type'] || 'text/plain';
      
      let extractedText = '';
      
      if (options.extractText) {
        if (contentType.includes('text/') || contentType.includes('application/json')) {
          extractedText = typeof content === 'string' ? content : JSON.stringify(content);
        } else {
          throw new ValidationError('Cannot extract text from this content type');
        }
      }

      const fileId = uuidv4();
      const fileName = path.basename(new URL(url).pathname) || 'downloaded_content';

      // Save to database
      const fileDoc = new FileModel({
        id: fileId,
        originalName: fileName,
        fileName: fileName,
        filePath: url, // Store URL as path for URL-based files
        mimeType: contentType,
        size: Buffer.byteLength(extractedText || content),
        extractedText,
        metadata: { url, ...options.metadata },
        source: options.source || 'url_download',
        processed: true,
        addedToKnowledgeBase: false,
      });

      await fileDoc.save();

      // Add to knowledge base if requested
      if (options.addToKnowledgeBase && extractedText) {
        await this.addToKnowledgeBase(fileId, extractedText, {
          title: fileName,
          source: options.source || 'url_download',
          url,
          ...options.metadata,
        });

        fileDoc.addedToKnowledgeBase = true;
        await fileDoc.save();
      }

      const duration = Date.now() - startTime;
      logDatabaseOperation('Process URL', 'Upload', duration, {
        url,
        contentType,
        size: fileDoc.size,
        extractedText: !!extractedText,
      });

      return {
        id: fileId,
        originalName: fileName,
        url,
        mimeType: contentType,
        size: fileDoc.size,
        extractedText: extractedText ? extractedText.substring(0, 500) + '...' : null,
        addedToKnowledgeBase: fileDoc.addedToKnowledgeBase,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logError('Failed to process URL', error, { url, options });
      throw new DatabaseError('URL processing failed');
    }
  }

  async getFile(id: string): Promise<any> {
    const startTime = Date.now();

    try {
      const file = await FileModel.findOne({ id });
      
      if (!file) {
        throw new NotFoundError(`File with id ${id} not found`);
      }

      const duration = Date.now() - startTime;
      logDatabaseOperation('Get file', 'Upload', duration, { id });

      return {
        id: file.id,
        originalName: file.originalName,
        fileName: file.fileName,
        filePath: file.filePath,
        mimeType: file.mimeType,
        size: file.size,
        metadata: file.metadata,
        source: file.source,
        processed: file.processed,
        addedToKnowledgeBase: file.addedToKnowledgeBase,
        createdAt: file.createdAt,
        updatedAt: file.updatedAt,
      };
    } catch (error) {
      if (error instanceof NotFoundError) throw error;
      logError('Failed to get file', error, { id });
      throw new DatabaseError('File retrieval failed');
    }
  }

  async getFileContent(id: string): Promise<any> {
    const startTime = Date.now();

    try {
      const file = await FileModel.findOne({ id });
      
      if (!file) {
        throw new NotFoundError(`File with id ${id} not found`);
      }

      const duration = Date.now() - startTime;
      logDatabaseOperation('Get file content', 'Upload', duration, { id });

      return {
        id: file.id,
        originalName: file.originalName,
        extractedText: file.extractedText,
        mimeType: file.mimeType,
        size: file.size,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      if (error instanceof NotFoundError) throw error;
      logError('Failed to get file content', error, { id });
      throw new DatabaseError('File content retrieval failed');
    }
  }

  async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  async listFiles(options: ListOptions): Promise<any> {
    const startTime = Date.now();

    try {
      const page = options.page || 1;
      const limit = Math.min(options.limit || 20, 100);
      const skip = (page - 1) * limit;

      // Build query
      const query: any = {};
      
      if (options.type) {
        query.mimeType = { $regex: options.type, $options: 'i' };
      }
      
      if (options.source) {
        query.source = options.source;
      }
      
      if (options.search) {
        query.$or = [
          { originalName: { $regex: options.search, $options: 'i' } },
          { extractedText: { $regex: options.search, $options: 'i' } },
        ];
      }

      // Execute query
      const [files, totalCount] = await Promise.all([
        FileModel.find(query)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .lean(),
        FileModel.countDocuments(query),
      ]);

      const duration = Date.now() - startTime;
      logDatabaseOperation('List files', 'Upload', duration, {
        resultCount: files.length,
        totalCount,
        page,
        limit,
      });

      return {
        files: files.map(file => ({
          id: file.id,
          originalName: file.originalName,
          fileName: file.fileName,
          mimeType: file.mimeType,
          size: file.size,
          source: file.source,
          processed: file.processed,
          addedToKnowledgeBase: file.addedToKnowledgeBase,
          createdAt: file.createdAt,
          updatedAt: file.updatedAt,
        })),
        pagination: {
          page,
          limit,
          totalCount,
          totalPages: Math.ceil(totalCount / limit),
          hasNext: page * limit < totalCount,
          hasPrev: page > 1,
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logError('Failed to list files', error, { options });
      throw new DatabaseError('File listing failed');
    }
  }
